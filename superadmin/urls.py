from django.urls import path
from . import views


app_name = "superadmin"

urlpatterns = [
    # Authentication
    path('login', views.AdminLoginAPIView.as_view(), name="admin-login"),

    # Dashboard
    path('dashboard', views.AdminDashboardAPIView.as_view(), name="admin-dashboard"),

    # Company Management
    path('companies', views.CompanyListAPIView.as_view(), name="company-list"),
    path('companies/<uuid:company_id>/customers', views.CompanyCustomersAPIView.as_view(), name="company-customers"),
    path('customers/set-inactive', views.SetCustomerInactiveAPIView.as_view(), name="set-customer-inactive"),

    # Account Creation Requests
    path('account-requests', views.AccountCreationRequestListAPIView.as_view(), name="account-requests"),
    path('account-requests/process', views.ProcessAccountRequestAPIView.as_view(), name="process-account-request"),

    # Transfer Management
    # path('transfers', views.TransferListAPIView.as_view(), name="transfer-list"),

    # Admin User Management
    path('admin-users', views.AdminUserListAPIView.as_view(), name="admin-user-list"),
    path('admin-users/change-permissions', views.ChangeAdminPermissionAPIView.as_view(), name="change-admin-permissions"),
    path('permissions', views.ChangeAdminPermissionAPIView.as_view(), name="change-admin-permissions"),
]

