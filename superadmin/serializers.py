import json

from django.contrib.auth import authenticate
from django.utils import timezone
from rest_framework import serializers
from rest_framework_simplejwt.tokens import RefreshToken

from account.models import Company, Customer
from account.serializers import CompanyCreationRequestOut
from transfer.models import SingleTransferRequest, BulkTransferRequest
from bowenmfb.modules.exceptions import InvalidRequestException
from .models import AdminUser, AdminRole, AdminPermission, AccountCreationRequest
from bowenmfb.modules.bankone import BankOneClient
from .tasks import create_corporate_account_with_bowen

client = BankOneClient()


class AdminLoginSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        password = validated_data.get("password")

        user = authenticate(username=email, password=password)
        if not user:
            raise InvalidRequestException({"detail": "Invalid email or password"})

        try:
            admin_user = AdminUser.objects.get(user=user, is_active=True)
        except AdminUser.DoesNotExist:
            raise InvalidRequestException({"detail": "User is not an admin or account is inactive"})

        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        return {
            "detail": "Login successful",
            "access_token": str(access_token),
            "refresh_token": str(refresh),
            "user": {
                "id": str(admin_user.id),
                "email": user.email,
                "full_name": user.get_full_name(),
                "role": admin_user.role.name if admin_user.role else None,
                "permissions": list(admin_user.permissions.values_list('name', flat=True))
            }
        }


class CompanySerializerOut(serializers.ModelSerializer):
    total_customers = serializers.SerializerMethodField()
    active_customers = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = ['id', 'name', 'email', 'phone_number', 'address', 'website', 'active', 'created_at', 'total_customers', 'active_customers']

    def get_total_customers(self, obj):
        return obj.customer_set.count()

    def get_active_customers(self, obj):
        return obj.customer_set.filter(active=True).count()


class CustomerSerializerOut(serializers.ModelSerializer):
    email = serializers.CharField(source='user.email')
    full_name = serializers.CharField(source='user.get_full_name')
    company_name = serializers.CharField(source='company.name')

    class Meta:
        model = Customer
        fields = ['id', 'email', 'full_name', 'phone_number', 'company_name', 'is_verified', 'active', 'created_at']


class SetCustomerInactiveSerializerIn(serializers.Serializer):
    customer_id = serializers.UUIDField()
    reason = serializers.CharField(max_length=500, required=False)

    def create(self, validated_data):
        customer_id = validated_data.get("customer_id")

        try:
            customer = Customer.objects.get(id=customer_id)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "Customer not found"})

        customer.active = False
        customer.save()

        return {"detail": f"Customer {customer.user.get_full_name()} has been set as inactive"}


class AccountCreationRequestSerializerOut(serializers.ModelSerializer):
    company_data = serializers.SerializerMethodField()
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', allow_null=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', allow_null=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', allow_null=True)

    def get_company_data(self, obj):
        return CompanyCreationRequestOut(obj.creation_request).data

    class Meta:
        model = AccountCreationRequest
        exclude = []


class ProcessAccountRequestSerializerIn(serializers.Serializer):
    request_id = serializers.UUIDField()
    action = serializers.ChoiceField(choices=['check', 'verify', 'approve', 'decline'])
    remarks = serializers.CharField(max_length=500, required=False)

    def create(self, validated_data):
        request_id = validated_data.get("request_id")
        action = validated_data.get("action")
        remarks = validated_data.get("remarks", "")

        # Get admin user from context
        admin_user = self.context['admin_user']

        try:
            account_request = AccountCreationRequest.objects.get(id=request_id)
        except AccountCreationRequest.DoesNotExist:
            raise InvalidRequestException({"detail": "Account creation request not found"})

        current_time = timezone.now()

        if action == 'check':
            if account_request.checked_by or account_request.approved_by:
                raise InvalidRequestException({"detail": "Request has passed the check stage"})
            account_request.checked_by = admin_user
            account_request.checked_at = current_time
        elif action == 'verify':
            if account_request.approved_by:
                raise InvalidRequestException({"detail": "Request has passed the verification stage"})
            if not account_request.checked_by:
                raise InvalidRequestException({"detail": "Request must be checked before verification"})
            account_request.verified_by = admin_user
            account_request.verified_at = current_time
        elif action == 'approve':
            if account_request.rejected_by:
                raise InvalidRequestException({"detail": "Request was initially rejected"})
            if not account_request.verified_by:
                raise InvalidRequestException({"detail": "Request must be verified before approval"})
            account_request.approved_by = admin_user
            account_request.approved_at = current_time
            account_request.status = 'success'
            create_corporate_account_with_bowen.delay(str(account_request.id), str(admin_user.id))
        elif action == 'decline':
            if account_request.approved_by:
                raise InvalidRequestException({"detail": "Cannot change status for approved requests"})
            if not remarks:
                raise InvalidRequestException({"detail": "Please add a reason for declining this request"})
            account_request.status = 'failed'
            account_request.rejected_by = admin_user
            account_request.rejected_at = current_time

        account_request.remarks = remarks
        account_request.save()

        return {"detail": f"Account request has been {action}ed successfully"}


class SingleTransferSerializerOut(serializers.ModelSerializer):
    """Serializer for single transfer output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)

    class Meta:
        model = SingleTransferRequest
        fields = ['id', 'company_name', 'amount', 'description', 'beneficiary_name',
                  'beneficiary_account_number', 'bank_name', 'is_checked', 'is_verified',
                  'is_approved', 'is_declined', 'created_at']


class BulkTransferSerializerOut(serializers.ModelSerializer):
    """Serializer for bulk transfer output"""
    company_name = serializers.CharField(source='company.name', allow_null=True)

    class Meta:
        model = BulkTransferRequest
        fields = ['id', 'company_name', 'total_amount', 'total_count', 'description',
                  'is_checked', 'is_verified', 'is_approved', 'is_declined', 'created_at']


class AdminUserSerializerOut(serializers.ModelSerializer):
    """Serializer for admin user output"""
    email = serializers.CharField(source='user.email')
    full_name = serializers.CharField(source='user.get_full_name')
    role_name = serializers.CharField(source='role.name', allow_null=True)
    permissions = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', allow_null=True)

    class Meta:
        model = AdminUser
        fields = ['id', 'email', 'full_name', 'role_name', 'permissions',
                  'is_active', 'created_by_name', 'created_at']

    def get_permissions(self, obj):
        return list(obj.permissions.values_list('name', flat=True))


class ChangeAdminPermissionSerializerIn(serializers.Serializer):
    """Serializer for changing admin permissions"""
    admin_user_id = serializers.UUIDField()
    role = serializers.ChoiceField(choices=['checker', 'verifier', 'approver'], required=False)
    permissions = serializers.ListField(
        child=serializers.ChoiceField(choices=[
            'view_companies', 'manage_company_users', 'check_account_requests',
            'verify_account_requests', 'approve_account_requests', 'view_transfers',
            'manage_admin_permissions'
        ]),
        required=False
    )

    def create(self, validated_data):
        admin_user_id = validated_data.get("admin_user_id")
        role = validated_data.get("role")
        permissions = validated_data.get("permissions", [])

        # Get current admin user from context
        current_admin = self.context['admin_user']

        try:
            target_admin = AdminUser.objects.get(id=admin_user_id)
        except AdminUser.DoesNotExist:
            raise InvalidRequestException({"detail": "Admin user not found"})

        # Update role if provided
        if role:
            try:
                admin_role = AdminRole.objects.get(name=role)
                target_admin.role = admin_role
                target_admin.save()
            except AdminRole.DoesNotExist:
                raise InvalidRequestException({"detail": "Invalid role specified"})

        # Update permissions if provided
        if permissions:
            target_admin.permissions.clear()
            for perm_name in permissions:
                try:
                    permission = AdminPermission.objects.get(name=perm_name)
                    target_admin.permissions.add(permission)
                except AdminPermission.DoesNotExist:
                    pass

        return {
            "detail": f"Permissions updated for {target_admin.user.get_full_name()}",
            "admin_user": AdminUserSerializerOut(target_admin).data
        }


class AdminPermissionSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = AdminPermission
        exclude = []

