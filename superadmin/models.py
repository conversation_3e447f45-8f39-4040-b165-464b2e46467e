from django.contrib.auth.models import User
from django.db import models

from account.models import BaseModel, CompanyCreationRequest
from bowenmfb.modules.choices import ADMIN_ROLE_CHOICES, ADMIN_PERMISSION_CHOICES, STATUS_CHOICES


class AdminRole(BaseModel):
    """Admin role model for RBAC system"""
    name = models.CharField(max_length=50, choices=ADMIN_ROLE_CHOICES, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.get_name_display()


class AdminPermission(BaseModel):
    """Admin permission model for RBAC system"""
    name = models.CharField(max_length=100, choices=ADMIN_PERMISSION_CHOICES, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.get_name_display()


class AdminUser(BaseModel):
    """Admin user model that inherits from User with RBAC capabilities"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.ForeignKey(AdminRole, on_delete=models.SET_NULL, null=True, blank=True)
    permissions = models.ManyToManyField(AdminPermission, blank=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.role.name if self.role else 'No Role'})"

    def has_permission(self, permission_name):
        """Check if admin user has specific permission"""
        return self.permissions.filter(name=permission_name).exists()

    def get_role_permissions(self):
        """Get default permissions based on role"""
        if not self.role:
            return []

        role_permissions = {
            'checker': ['view_companies', 'check_account_requests', 'view_transfers'],
            'verifier': ['view_companies', 'check_account_requests', 'verify_account_requests', 'view_transfers'],
            'approver': ['view_companies', 'manage_company_users', 'check_account_requests',
                        'verify_account_requests', 'approve_account_requests', 'view_transfers',
                        'manage_admin_permissions']
        }

        return role_permissions.get(self.role.name, [])

    def save(self, *args, **kwargs):
        """Override save to set default permissions based on role"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new and self.role:
            # Set default permissions based on role
            default_permissions = self.get_role_permissions()
            for perm_name in default_permissions:
                try:
                    permission = AdminPermission.objects.get(name=perm_name)
                    self.permissions.add(permission)
                except AdminPermission.DoesNotExist:
                    pass


class AccountCreationRequest(BaseModel):
    """Model to track account creation requests for admin approval"""
    creation_request = models.ForeignKey(CompanyCreationRequest, on_delete=models.CASCADE)
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='pending')

    # RBAC tracking fields
    checked_by = models.ForeignKey(AdminUser, related_name='checked_requests', on_delete=models.SET_NULL, null=True, blank=True)
    verified_by = models.ForeignKey(AdminUser, related_name='verified_requests', on_delete=models.SET_NULL, null=True, blank=True)
    approved_by = models.ForeignKey(AdminUser, related_name='approved_requests', on_delete=models.SET_NULL, null=True, blank=True)
    rejected_by = models.ForeignKey(AdminUser, on_delete=models.SET_NULL, blank=True, null=True, related_name="rejected_requests")

    checked_at = models.DateTimeField(null=True, blank=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)

    remarks = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.creation_request.business_name}"
