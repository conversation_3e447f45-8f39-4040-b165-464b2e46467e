from drf_spectacular.utils import extend_schema
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from bowenmfb.modules.exceptions import raise_serializer_error_msg
from bowenmfb.modules.permissions import (
    IsAdminUser, CanViewCompanies, CanManageCompanyUsers,
    CanCheckAccountRequests, CanVerifyAccountRequests, CanApproveAccountRequests,
    CanViewTransfers, CanManageAdminPermissions
)
from .serializers import *


class AdminLoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=AdminLoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = AdminLoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class CompanyListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAd<PERSON><PERSON>ser, CanViewCompanies]

    @extend_schema(responses={status.HTTP_200_OK: CompanySerializerOut(many=True)})
    def get(self, request):
        companies = Company.objects.all().order_by('-created_at')
        serializer = CompanySerializerOut(companies, many=True)
        return Response({
            "detail": "Companies retrieved successfully",
            "data": serializer.data
        })


class CompanyCustomersAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)})
    def get(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"detail": "Company not found"}, status=status.HTTP_404_NOT_FOUND)

        customers = Customer.objects.filter(company=company).order_by('-created_at')
        serializer = CustomerSerializerOut(customers, many=True)
        return Response({
            "detail": "Company customers retrieved successfully",
            "company": company.name,
            "data": serializer.data
        })


class SetCustomerInactiveAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=SetCustomerInactiveSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = SetCustomerInactiveSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class AccountCreationRequestListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanCheckAccountRequests]

    @extend_schema(responses={status.HTTP_200_OK: AccountCreationRequestSerializerOut(many=True)})
    def get(self, request):
        # Filter by status if provided
        status_filter = request.query_params.get('status', None)
        requests = AccountCreationRequest.objects.all()

        if status_filter:
            requests = requests.filter(status=status_filter)

        requests = requests.order_by('-created_at')
        serializer = AccountCreationRequestSerializerOut(requests, many=True)
        return Response({
            "detail": "Account creation requests retrieved successfully",
            "data": serializer.data
        })


class ProcessAccountRequestAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_permissions(self):
        permissions = [IsAuthenticated, IsAdminUser]

        if hasattr(self, 'request') and self.request.data:
            action = self.request.data.get('action')
            if action == 'check':
                permissions.append(CanCheckAccountRequests)
            elif action == 'verify':
                permissions.append(CanVerifyAccountRequests)
            elif action in ['approve', 'decline']:
                permissions.append(CanApproveAccountRequests)

        return [permission() for permission in permissions]

    @extend_schema(request=ProcessAccountRequestSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ProcessAccountRequestSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TransferListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewTransfers]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        transfer_type = request.query_params.get('type', 'all')  # 'single', 'bulk', or 'all'

        response_data = {}

        # Filter single transfers
        single_transfers = SingleTransferRequest.objects.all()
        if company_id:
            single_transfers = single_transfers.filter(company_id=company_id)

        # Filter bulk transfers
        bulk_transfers = BulkTransferRequest.objects.all()
        if company_id:
            bulk_transfers = bulk_transfers.filter(company_id=company_id)

        if transfer_type == 'single':
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Single transfers retrieved successfully",
                "single_transfers": single_serializer.data
            }
        elif transfer_type == 'bulk':
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Bulk transfers retrieved successfully",
                "bulk_transfers": bulk_serializer.data
            }
        else:  # 'all'
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "All transfers retrieved successfully",
                "single_transfers": single_serializer.data,
                "bulk_transfers": bulk_serializer.data
            }

        return Response(response_data)


class AdminUserListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(responses={status.HTTP_200_OK: AdminUserSerializerOut(many=True)})
    def get(self, request):
        admin_users = AdminUser.objects.filter(is_active=True).order_by('-created_at')
        serializer = AdminUserSerializerOut(admin_users, many=True)
        return Response({
            "detail": "Admin users retrieved successfully",
            "data": serializer.data
        })


class ChangeAdminPermissionAPIView(APIView):
    """Change admin user permissions - requires manage_admin_permissions permission"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(request=ChangeAdminPermissionSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ChangeAdminPermissionSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class AdminDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Gather statistics based on permissions
        dashboard_data = {
            "admin_info": {
                "name": admin_user.user.get_full_name(),
                "role": admin_user.role.name if admin_user.role else None,
                "permissions": list(admin_user.permissions.values_list('name', flat=True))
            }
        }

        # Add statistics based on permissions
        if admin_user.has_permission('view_companies'):
            dashboard_data["companies"] = {
                "total": Company.objects.count(),
                "active": Company.objects.filter(active=True).count()
            }
            dashboard_data["customers"] = {
                "total": Customer.objects.count(),
                "active": Customer.objects.filter(active=True).count(),
                "verified": Customer.objects.filter(is_verified=True).count()
            }

        if admin_user.has_permission('check_account_requests'):
            dashboard_data["account_requests"] = {
                "pending": AccountCreationRequest.objects.filter(status='pending').count(),
                "approved": AccountCreationRequest.objects.filter(status='success').count(),
                "declined": AccountCreationRequest.objects.filter(status='failed').count()
            }

        if admin_user.has_permission('view_transfers'):
            dashboard_data["transfers"] = {
                "single_pending": SingleTransferRequest.objects.filter(is_approved=False, is_declined=False).count(),
                "single_approved": SingleTransferRequest.objects.filter(is_approved=True).count(),
                "bulk_pending": BulkTransferRequest.objects.filter(is_approved=False, is_declined=False).count(),
                "bulk_approved": BulkTransferRequest.objects.filter(is_approved=True).count()
            }

        return Response({
            "detail": "Dashboard data retrieved successfully",
            "data": dashboard_data
        })


class ListAdminPermissionListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated, CanManageAdminPermissions]
    queryset = AdminPermission
    serializer_class = AdminPermissionSerializerOut
