from rest_framework.permissions import BasePermission

from bowenmfb.modules.exceptions import MaximumApprovalPINRetriesException
from superadmin.models import AdminUser


class IsAdminUser(BasePermission):
    """
    Custom permission to only allow admin users to access views.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return True
        except AdminUser.DoesNotExist:
            return False


class HasAdminPermission(BasePermission):
    """
    Custom permission to check if admin user has specific permission.
    Usage: Add permission_required attribute to view class.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return False
        
        # Get required permission from view
        required_permission = getattr(view, 'permission_required', None)
        if not required_permission:
            return True  # No specific permission required
        
        return admin_user.has_permission(required_permission)


class CanViewCompanies(BasePermission):
    """Permission to view companies"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('view_companies')
        except AdminUser.DoesNotExist:
            return False


class CanManageCompanyUsers(BasePermission):
    """Permission to manage company users (set as inactive)"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('manage_company_users')
        except AdminUser.DoesNotExist:
            return False


class CanCheckAccountRequests(BasePermission):
    """Permission to check account creation requests"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('check_account_requests')
        except AdminUser.DoesNotExist:
            return False


class CanVerifyAccountRequests(BasePermission):
    """Permission to verify account creation requests"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('verify_account_requests')
        except AdminUser.DoesNotExist:
            return False


class CanApproveAccountRequests(BasePermission):
    """Permission to approve account creation requests"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('approve_account_requests')
        except AdminUser.DoesNotExist:
            return False


class CanViewTransfers(BasePermission):
    """Permission to view transfers"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('view_transfers')
        except AdminUser.DoesNotExist:
            return False


class CanManageAdminPermissions(BasePermission):
    """Permission to manage other admin permissions (approver only)"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
            return admin_user.has_permission('manage_admin_permissions')
        except AdminUser.DoesNotExist:
            return False


class TooManyPINRetriesException(BasePermission):

    def has_permission(self, request, view):

        if not (request.user and request.user.is_authenticated):
            return False

        if request.user.customer.failed_pin_retries >= 5:
            raise MaximumApprovalPINRetriesException()

        return True

