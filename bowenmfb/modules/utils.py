import base64
import calendar
import datetime
import decimal
import logging
import uuid
from django.conf import settings

from dateutil.relativedelta import relativedelta
from cryptography.fernet import Fernet


def log_request(*args):
    for arg in args:
        logging.info(arg)


def format_phone_number(phone_number):
    phone_number = f"0{phone_number[-10:]}"
    return phone_number


def encrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    secure = fernet.encrypt(f"{text}".encode())
    return secure.decode()


def decrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    decrypt = fernet.decrypt(text.encode())
    return decrypt.decode()


def generate_transaction_reference():
    # Provider's length is 12 characters
    from transfer.models import SingleTransfer, Bulk<PERSON>ransfer
    from datetime import datetime

    now = datetime.now()
    year = str(now.year)[2:]
    month = str(now.month)
    if len(month) == 1:
        month = f"0{month}"
    unique_identifier = str(uuid.uuid4().int)[:7]

    while True:
        ref_code = f"B{year}{month}{unique_identifier}"
        print(ref_code)
        if not (SingleTransfer.objects.filter(reference=ref_code).exists() or
                BulkTransfer.objects.filter(reference=ref_code).exists()):
            return ref_code


def generate_random_otp(count: int):
    return str(uuid.uuid4().int)[:count]


def get_previous_date(date, delta):
    previous_date = date - datetime.timedelta(days=delta)
    return previous_date


def get_next_date(date, delta):
    next_date = date + datetime.timedelta(days=delta)
    return next_date


def get_next_weekday(date, weekday):
    days_ahead = weekday - date.weekday()
    if days_ahead <= 0:
        days_ahead += 7
    return date + datetime.timedelta(days_ahead)


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def get_previous_month_date(date, delta):
    return date - relativedelta(months=delta)


def mask_number(number):
    first_covered = number[0:3]
    second_covered = number[-3:]
    total_covered = first_covered + "******" + second_covered
    return total_covered


def get_next_minute(date, delta):
    next_minute = date + relativedelta(minutes=delta)
    return next_minute


def get_previous_minute(date, delta):
    previous_minute = date - relativedelta(minutes=delta)
    return previous_minute


def get_previous_seconds(date, delta):
    previous_seconds = date - relativedelta(seconds=delta)
    return previous_seconds


def get_previous_hour(date, delta):
    previous_hour = date - relativedelta(hours=delta)
    return previous_hour


def get_account_balance(account_number):
    from bowenmfb.modules.bankone import BankOneClient
    client = BankOneClient()

    available = 0.0
    ledger = 0.0
    withdraw_able = 0.0
    response = client.get_account_by_number(account_number)
    if "AvailableBalance" in response:
        available = decimal.Decimal(str(response["AvailableBalance"]).replace(",", "")) / 100
    if "LedgerBalance" in response:
        ledger = decimal.Decimal(str(response["LedgerBalance"]).replace(",", "")) / 100
    if "WithdrawableBalance" in response:
        withdraw_able = decimal.Decimal(str(response["WithdrawableBalance"]).replace(",", "")) / 100

    balance_data = {
        "account_number": mask_number(account_number),
        "balances": {
            "available_balance": available,
            "ledger_balance": ledger,
            "withdrawable_balance": withdraw_able
        }
    }

    return balance_data


def validate_bvn_with_phone_number(bvn_number, phone_number):
    from bowenmfb.modules.bankone import BankOneClient
    client = BankOneClient()

    response = client.get_bvn_details(bvn=bvn_number)

    if ("RequestStatus" in response and response["RequestStatus"] is True) and ("isBvnValid" in response and response["isBvnValid"] is True):
        bvn_phone_number = response["bvnDetails"]["phoneNumber"]
        if settings.DEBUG is True:
            bvn_phone_number = phone_number
        if str(phone_number).strip() == str(bvn_phone_number).strip():
            return True

    return False





