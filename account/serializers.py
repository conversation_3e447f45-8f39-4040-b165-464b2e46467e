import json
from datetime import timedelta

from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password, check_password
from django.contrib.auth.password_validation import validate_password
from django.db.models import Q
from django.utils import timezone
from rest_framework import serializers
from rest_framework_simplejwt.tokens import AccessToken

from bowenmfb.modules.exceptions import InvalidRequestException
from bowenmfb.modules.utils import format_phone_number, encrypt_text, generate_random_otp, log_request, get_next_minute, validate_bvn_with_phone_number
from superadmin.models import AccountCreationRequest
from .models import *


class BankConstantTableSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BankConstantTable
        exclude = ["auth_token"]


class AccountOfficerSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = AccountOfficer
        exclude = []


class CompanyAccountSerializerOut(serializers.ModelSerializer):
    account_officer = serializers.SerializerMethodField()
    account_number = serializers.CharField(source="get_masked_account_number")

    def get_account_officer(self, obj):
        return AccountOfficerSerializerOut(obj.account_officer).data if obj.account_officer else None

    class Meta:
        model = CompanyAccount
        exclude = ["company", "bank_one_account_number"]


class CompanySerializerOut(serializers.ModelSerializer):
    tax_number = serializers.CharField(source="get_masked_tax_number")
    registration_number = serializers.CharField(source="get_registration_number")
    accounts = serializers.SerializerMethodField()

    def get_accounts(self, obj):
        return CompanyAccountSerializerOut(CompanyAccount.objects.filter(company=obj), many=True).data

    class Meta:
        model = Company
        exclude = []


class CustomerSerializerOut(serializers.ModelSerializer):
    first_name = serializers.CharField(source="user.first_name")
    last_name = serializers.CharField(source="user.last_name")
    username = serializers.CharField(source="user.username")
    email = serializers.CharField(source="user.email")
    date_joined = serializers.CharField(source="user.date_joined")
    bvn_number = serializers.CharField(source="get_masked_bvn")
    nin_number = serializers.CharField(source="get_masked_nin")
    company = serializers.SerializerMethodField()

    def get_company(self, obj):
        return CompanySerializerOut(obj.company).data if obj.company else None

    class Meta:
        model = Customer
        exclude = ["approval_pin", "verification_token", "verification_token_expiry", "account_officer"]


class CompanyCreationRequestOut(serializers.ModelSerializer):
    signatories = serializers.SerializerMethodField()
    directors = serializers.SerializerMethodField()

    def get_signatories(self, obj):
        return json.loads(obj.signatories) if obj.signatories else ""

    def get_directors(self, obj):
        return json.loads(obj.directors) if obj.directors else ""

    class Meta:
        model = CompanyCreationRequest
        exclude = []


# class UserSerializerOut

class CustomerDetailSerializerIn(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    other_name = serializers.CharField(required=False)
    email = serializers.EmailField()
    address = serializers.CharField()
    gender = serializers.ChoiceField(choices=GENDER_TYPE_CHOICES)
    date_of_birth = serializers.CharField()
    phone_number = serializers.CharField()
    state_of_origin = serializers.CharField()
    nin_number = serializers.IntegerField()
    bvn_number = serializers.IntegerField()


class CompanyCreationRequestIn(serializers.Serializer):
    # BUSINESS DATA
    # cac_doc, board_resolution, reference_form
    business_name = serializers.CharField()
    business_email = serializers.EmailField()
    business_phone_number = serializers.IntegerField()
    business_address = serializers.CharField()
    # business_bvn = serializers.CharField()
    business_website = serializers.CharField(required=False)
    business_sector = serializers.CharField(required=False)
    business_tax_number = serializers.CharField(required=False)
    business_registration_number = serializers.CharField()
    business_registration_date = serializers.CharField()
    contact_person_name = serializers.CharField()
    contact_person_phone = serializers.IntegerField()
    cac_document = serializers.CharField()
    board_resolution = serializers.CharField()
    reference_form = serializers.CharField()
    signatories = serializers.ListSerializer(child=CustomerDetailSerializerIn())
    directors = serializers.ListSerializer(child=CustomerDetailSerializerIn())

    def validate(self, attrs):
        name = attrs.get("business_name")
        signatories = attrs.get("signatories")
        directors = attrs.get("directors")
        if Company.objects.filter(name__iexact=name).exists():
            raise InvalidRequestException({"detail": "Business name taken"})
        if CompanyCreationRequest.objects.filter(business_name__iexact=name).exists():
            raise InvalidRequestException({"detail": "Request with same business name already exist"})

        signatories_data = list()

        for director in directors:
            director_bvn = str(director.get("bvn_number")).strip()
            if not (str(director_bvn).isnumeric() or len(director_bvn) == 11):
                raise InvalidRequestException({"detail": "Director's BVN is not valid"})

            bvn_validation = validate_bvn_with_phone_number(director_bvn, director.get("phone_number"))
            if not bvn_validation:
                raise InvalidRequestException({"detail": f"BVN: {director_bvn} is not valid or BVN registered phone number mismatch"})

            signatories_data.append(director.get("email"))
            signatories_data.append(director_bvn)
            signatories_data.append(director.get("nin_number"))

        for director in signatories:
            director_bvn = str(director.get("bvn_number")).strip()
            if not (str(director_bvn).isnumeric() or len(director_bvn) == 11):
                raise InvalidRequestException({"detail": "Signatory's BVN is not valid"})
            signatories_data.append(director.get("email"))
            signatories_data.append(director_bvn)
            signatories_data.append(director.get("nin_number"))

            bvn_validation = validate_bvn_with_phone_number(director_bvn, director.get("phone_number"))
            if not bvn_validation:
                raise InvalidRequestException({"detail": f"BVN: {director_bvn} is not valid or BVN registered phone number mismatch"})


        # if len(signatories_data) > len(set(signatories_data)):
        #     raise InvalidRequestException({
        #         "detail": "There are duplicates in Signatories and Directors' data. Check email, BVN Number and NIN Number for uniqueness"
        #     })

        return attrs

    def create(self, validated_data):
        business_name = validated_data.get("business_name")
        business_phone_number = validated_data.get("business_phone_number")
        business_email = validated_data.get("business_email")
        business_address = validated_data.get("business_address")
        business_website = validated_data.get("business_website")
        # business_bvn = validated_data.get("business_bvn")
        business_sector = validated_data.get("business_sector")
        business_tax_number = validated_data.get("business_tax_number")
        business_registration_number = validated_data.get("business_registration_number")
        business_registration_date = validated_data.get("business_registration_date")
        contact_person_name = validated_data.get("contact_person_name")
        contact_person_phone = validated_data.get("contact_person_phone")
        signatories = validated_data.get("signatories")
        directors = validated_data.get("directors")
        cac_document = validated_data.get("cac_document")
        board_resolution = validated_data.get("board_resolution")
        reference_form = validated_data.get("reference_form")

        if signatories:
            signatories = json.dumps(signatories)

        create_request = CompanyCreationRequest.objects.create(
            business_email=business_email, business_name=business_name, business_phone_number=business_phone_number,
            business_address=business_address, business_website=business_website, business_sector=business_sector,
            contact_person_name=contact_person_name, contact_person_phone=contact_person_phone, business_tax_number=business_tax_number,
            business_registration_number=business_registration_number, business_registration_date=business_registration_date, signatories=signatories,
            directors=json.dumps(directors), cac_document=cac_document, board_resolution=board_resolution, reference_form=reference_form
        )

        AccountCreationRequest.objects.create(creation_request=create_request)

        return {"detail": "Account creation request submitted successfully. Your request will be reviewed shortly"}


class SignUpSerializerIn(CustomerDetailSerializerIn):
    company_id = serializers.CharField()
    transaction_pin = serializers.CharField(max_length=4)
    profile_picture = serializers.CharField(required=False)
    username = serializers.CharField()
    password = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    other_name = serializers.CharField()
    email = serializers.EmailField()
    address = serializers.CharField()
    gender = serializers.ChoiceField(choices=GENDER_TYPE_CHOICES)
    date_of_birth = serializers.DateTimeField()
    phone_number = serializers.CharField()
    state_of_origin = serializers.CharField()
    nin_number = serializers.CharField()
    bvn_number = serializers.CharField()

    def validate(self, attrs):
        username = attrs.get("username")
        email = attrs.get("email")
        transaction_pin = attrs.get("transaction_pin")
        company_id = attrs.get("company_id")
        bvn_number = attrs.get("bvn_number")
        phone_number = attrs.get("phone_number")

        bvn_validation = validate_bvn_with_phone_number(bvn_number, phone_number)
        if not bvn_validation:
            raise InvalidRequestException({"detail": f"BVN: {bvn_number} is not valid or BVN registered phone number mismatch"})

        try:
            Company.objects.get(bank_customer_id=company_id)
        except Company.DoesNotExist:
            raise InvalidRequestException({"detail": "Company not found"})

        query = Q(email__iexact=email) | Q(username__iexact=username)

        if User.objects.filter(query).exists():
            raise InvalidRequestException({"detail": "Username or email already taken"})
        if not (str(transaction_pin).isnumeric() or len(transaction_pin) == 4):
            raise InvalidRequestException({"detail": "Transaction PIN must be four (4) digits"})

        return attrs

    def validate_password(self, password):
        user = User(
            username=self.initial_data.get("username"),
            email=self.initial_data.get("email")
        )
        validate_password(password, user)
        return password

    def create(self, validated_data):
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        other_name = validated_data.get("other_name")
        email = validated_data.get("email")
        address = validated_data.get("address")
        gender = validated_data.get("gender")
        date_of_birth = validated_data.get("date_of_birth")
        phone_number = validated_data.get("phone_number")
        state_of_origin = validated_data.get("state_of_origin")
        nin_number = validated_data.get("nin_number")
        bvn_number = validated_data.get("bvn_number")
        company_id = validated_data.get("company_id")
        transaction_pin = validated_data.get("transaction_pin")
        profile_picture = validated_data.get("profile_picture")
        username = validated_data.get("username")
        password = validated_data.get("password")

        company = Company.objects.get(bank_customer_id=company_id)

        verification_token = generate_random_otp(6)
        log_request(f"Created account for {email} - {verification_token}")

        user = User.objects.create(email=email, username=username, first_name=first_name, last_name=last_name, password=make_password(password))
        Customer.objects.create(
            user=user, other_name=other_name, address=address, dob=date_of_birth, gender=gender, phone_number=format_phone_number(phone_number),
            state_of_origin=state_of_origin, bvn_number=encrypt_text(bvn_number), nin_number=encrypt_text(nin_number),
            approval_pin=encrypt_text(transaction_pin), image=profile_picture, company=company, verification_token=encrypt_text(verification_token),
            verification_token_expiry=timezone.now() + timedelta(minutes=15)
        )
        detail = {"detail": "Account created successfully. Please check your email for verification code"}
        if settings.DEBUG is True:
            detail["verification_code"] = verification_token

        return detail


class LoginSerializerIn(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()

    def create(self, validated_data):
        username = validated_data.get("username")
        password = validated_data.get("password")

        user = authenticate(username=username, password=password)
        if not user:
            raise InvalidRequestException({"detail": "Invalid email/username or password"})

        customer = Customer.objects.get(user=user)

        if not customer.is_verified:
            new_verification_token = generate_random_otp(6)
            log_request(f"Email Token for email - {user.email}: {new_verification_token}")
            customer.verification_token = encrypt_text(new_verification_token)
            customer.verification_token_expiry = get_next_minute(timezone.datetime.now(), 15)
            customer.save()

            detail = {"detail": "Pending account verification. Please check your email for a new verification code"}
            if settings.DEBUG is True:
                detail["verification_code"] = new_verification_token

            # Send verification token to customer
            raise InvalidRequestException(detail)
        access_token = str(AccessToken.for_user(user))
        return {"detail": "Login successful", "data": CustomerSerializerOut(customer).data, "access_token": access_token}


class EmailVerificationSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    verification_code = serializers.CharField()

    def create(self, validated_data):
        email = validated_data.get("email")
        verification_token = validated_data.get("verification_code")

        customers = Customer.objects.filter(user__email__iexact=email)

        if not customers.exists():
            raise InvalidRequestException({"detail": "User not found"})

        customer = customers.last()

        if customer.is_verified:
            raise InvalidRequestException({"detail": "Account is already verified, please proceed to login"})

        if verification_token != decrypt_text(customer.verification_token):
            raise InvalidRequestException({"detail": "You have provided an invalid verification code"})

        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})

        customer.is_verified = True
        customer.verification_token = ""
        customer.save()

        # Send Welcome Email to user
        return {"detail": "Your account is successfully verified, please proceed to login"}


class RequestVerificationLinkSerializerIn(serializers.Serializer):
    email = serializers.EmailField()

    def create(self, validated_data):
        email = validated_data.get("email")
        try:
            customer = Customer.objects.get(user__email=email)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "User with this email is not found"})

        verification_token = generate_random_otp(6)
        log_request(f"Email Token for email - {email}: {verification_token}")
        customer.verification_token = encrypt_text(verification_token)
        customer.verification_token_expiry = get_next_minute(timezone.datetime.now(), 15)
        customer.save()

        detail = {"detail": "Verification code sent to your email"}
        if settings.DEBUG is True:
            detail["verification_code"] = verification_token

        # Send email verification link to user
        return detail


class ChangePasswordSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    old_password = serializers.CharField(required=False)
    new_password = serializers.CharField()
    confirm_password = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        old_password = validated_data.get("old_password")
        new_password = validated_data.get("new_password")
        confirm_password = validated_data.get("confirm_password")

        if not all([old_password, new_password, confirm_password]):
            raise InvalidRequestException({"detail": "All password fields are required"})

        if not check_password(password=old_password, encoded=user.password):
            raise InvalidRequestException({"detail": "Incorrect old password"})

        if new_password != confirm_password:
            raise InvalidRequestException({"detail": "Passwords mismatch"})

        # Check if new and old passwords are the same
        if old_password == new_password:
            raise InvalidRequestException({"detail": "Same passwords cannot be used"})

        try:
            validate_password(new_password, user)
        except Exception as err:
            raise InvalidRequestException({"detail": f"{err}"})

        user.password = make_password(password=new_password)
        user.save()

        return {"detail": "Password Reset Successful"}


class ResetPasswordSerializerIn(serializers.Serializer):
    verification_code = serializers.CharField()
    password = serializers.CharField()
    email = serializers.EmailField()

    def create(self, validated_data):
        verification_code = validated_data.get("verification_code")
        password = validated_data.get("password")
        email = validated_data.get("email")

        try:
            customer = Customer.objects.get(user__email__iexact=email)
        except Customer.DoesNotExist:
            raise InvalidRequestException({"detail": "User not found"})

        # Check OTP expiry
        if timezone.now() > customer.verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})
        # Compare token
        if verification_code != decrypt_text(customer.verification_token):
            raise InvalidRequestException({"detail": "Verification code is NOT valid"})

        user = customer.user

        try:
            validate_password(password, user)
        except Exception as err:
            raise InvalidRequestException({"detail": f"{err}"})

        user.set_password(raw_password=password)
        user.save()
        return {"detail": "Password reset was successful"}


class BankConstantTableSerializerIn(serializers.Serializer):
    auth_token = serializers.CharField(required=False)
    short_name = serializers.CharField(required=False)
    support_email = serializers.CharField(required=False)
    support_phone = serializers.CharField(required=False)
    website = serializers.CharField(required=False)
    address = serializers.CharField(required=False)
    mfb_code = serializers.CharField(required=False)
    app_version = serializers.CharField(required=False)
    institution_code = serializers.CharField(required=False)
    name = serializers.CharField(required=False)
    dev_base_url = serializers.CharField(required=False)
    prod_base_url = serializers.CharField(required=False)

    def create(self, validated_data):
        auth_token = validated_data.get("auth_token")
        name = validated_data.get("name", "Bowen Micro Finance Bank")

        bank_config, created = BankConstantTable.objects.get_or_create(name=name)

        bank_config.institution_code = validated_data.get("institution_code", bank_config.institution_code)
        bank_config.short_name = validated_data.get("short_name", bank_config.short_name)
        bank_config.support_email = validated_data.get("support_email", bank_config.support_email)
        bank_config.support_phone = validated_data.get("support_phone", bank_config.support_phone)
        bank_config.website = validated_data.get("website", bank_config.website)
        bank_config.address = validated_data.get("address", bank_config.address)
        bank_config.mfb_code = validated_data.get("mfb_code", bank_config.mfb_code)
        bank_config.app_version = validated_data.get("app_version", bank_config.app_version)
        bank_config.dev_base_url = validated_data.get("dev_base_url", bank_config.dev_base_url)
        bank_config.prod_base_url = validated_data.get("prod_base_url", bank_config.prod_base_url)

        if auth_token:
            bank_config.auth_token = encrypt_text(auth_token)

        bank_config.save()

        return BankConstantTableSerializerOut(bank_config, context={"request": self.context.get("request")}).data


