from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from bowenmfb.modules.bankone import BankOneClient
from transfer.models import BankList
from transfer.serializers import BankListSerializerOut
from .serializers import *
from bowenmfb.modules.exceptions import raise_serializer_error_msg, BankOneAPIError


class AccountCreationAPIView(APIView):
    permission_classes = []

    @extend_schema(request=CompanyCreationRequestIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CompanyCreationRequestIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class SignUpAPIView(APIView):
    permission_classes = []

    @extend_schema(request=SignUpSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = SignUpSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class LoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=LoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = LoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class EmailVerificationAPIView(APIView):
    permission_classes = []

    @extend_schema(request=EmailVerificationSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = EmailVerificationSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class RequestVerificationCodeAPIView(APIView):
    permission_classes = []

    @extend_schema(request=RequestVerificationLinkSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = RequestVerificationLinkSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ChangePasswordAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ChangePasswordSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ResetPasswordAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ResetPasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ResetPasswordSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class BankConstantUpdateAPIView(APIView):
    permission_classes = []  # This is temporal
    
    @extend_schema(request=BankConstantTableSerializerIn, responses={status.HTTP_200_OK: BankConstantTableSerializerOut})
    def post(self, request):
        serializer = BankConstantTableSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class FetchAccountOfficersAPIView(APIView):
    permission_classes = []  # Consider adding proper permissions in production

    @extend_schema(responses={status.HTTP_200_OK: AccountOfficerSerializerOut(many=True)})
    def post(self, request):
        try:
            client = BankOneClient()
            response = client.get_account_officers()
            created_count = 0
            updated_count = 0

            # response = [{"Name":"DUREKE, PAUL","Code":"003","Branch":None,"Gender":None,"PhoneNumber":None,"Email":None,"Id":None}]

            for officer_data in response:
                officer, created = AccountOfficer.objects.update_or_create(
                    code=officer_data.get("Code"),
                    defaults={
                        "name": officer_data.get("Name"),
                        "email": officer_data.get("Email"),
                        "gender": officer_data.get("Gender"),
                        "phone_number": officer_data.get("PhoneNumber")
                    }
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            # Return all account officers
            officers = AccountOfficer.objects.all()
            serializer = AccountOfficerSerializerOut(officers, many=True)

            return Response({
                "message": f"Successfully processed account officers. Created: {created_count}, Updated: {updated_count}",
                "data": serializer.data
            })

        except BankOneAPIError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateBankListAPIView(APIView):
    permission_classes = []  # Consider adding proper permissions in production

    @extend_schema(responses={status.HTTP_200_OK: AccountOfficerSerializerOut(many=True)})
    def post(self, request):
        try:
            client = BankOneClient()
            response = client.get_commercial_banks()
            created_count = 0
            updated_count = 0
            """
            [
                {
                    "Code": "076",
                    "Gateway": null,
                    "ID": "1",
                    "Name": "Skye Bank",
                    "Status": false,
                    "StatusDetails": null,
                    "RequestStatus": false,
                    "ResponseDescription": null,
                    "ResponseStatus": null
                },
                {
                    "Code": "035",
                    "Gateway": null,
                    "ID": "2",
                    "Name": "Wema Bank",
                    "Status": false,
                    "StatusDetails": null,
                    "RequestStatus": false,
                    "ResponseDescription": null,
                    "ResponseStatus": null
                },
            ]
            """

            for bank in response:
                officer, created = BankList.objects.update_or_create(
                    code=bank.get("Code"),
                    defaults={
                        "name": bank.get("Name"),
                    }
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            # Return all account officers
            bank_list = BankList.objects.all()
            serializer = BankListSerializerOut(bank_list, many=True)

            return Response({
                "message": f"Successfully updated bank lists. Created: {created_count}, Updated: {updated_count}",
                "data": serializer.data
            })

        except BankOneAPIError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





