from django.urls import path
from . import views


app_name = "account"

urlpatterns = [
    path('create-account', views.AccountCreationAPIView.as_view(), name="create-account"),
    path('register', views.SignUpAPIView.as_view(), name="register"),
    path('login', views.LoginAPIView.as_view(), name="login"),
    path('verify-email', views.EmailVerificationAPIView.as_view(), name="verify-email"),
    path('request-verification-code', views.RequestVerificationCodeAPIView.as_view(), name="request-verification-code"),
    path('change-password', views.ChangePasswordAPIView.as_view(), name="change-password"),
    path('reset-password', views.ResetPasswordAPIView.as_view(), name="reset-password"),
    path('create-additional-account', views.CreateAdditionalAccountAPIView.as_view(), name="create-additional-account")
]

