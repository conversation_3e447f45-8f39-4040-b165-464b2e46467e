from django.urls import path
from . import views


app_name = "transfer"

urlpatterns = [
    path('banks', views.ListBankGenericAPIView.as_view(), name="banks"),
    path('name-enquiry', views.NameEnquiryAPIView.as_view(), name="name-enquiry"),
    path('balance-enquiry', views.AccountBalanceAPIView.as_view(), name="balance-enquiry"),
    path('history', views.TransactionHistoryAPIView.as_view(), name="history"),

    # Signatory Management
    path('signatories', views.AccountSignatoryListAPIView.as_view(), name="account-signatories"),
    path('signatory-hierarchy', views.SignatoryHierarchyAPIView.as_view(), name="signatory-hierarchy"),

    # Transfer Approval Workflow
    path('transfer-approval', views.TransferApprovalWorkflowAPIView.as_view(), name="transfer-workflow"),
    path('transfer-approval/process', views.ProcessTransferApprovalAPIView.as_view(), name="process-transfer-approval"),

    # Bulk Transfer Template
    path('bulk-template', views.BulkTransferTemplateDownloadAPIView.as_view(), name="bulk-transfer-template"),

    # Single Transfer
    path('single-transfer-request', views.CreateSingleTransferRequestAPIView.as_view(), name="single-transfer-request"),
    path('single-transfer-request/list', views.ListSingleTransferRequest.as_view(), name="single-transfer-request-list"),
    path('single-transfer-request/list/<str:id>', views.ListSingleTransferRequest.as_view(), name="single-transfer-request-list-detail"),
    path('single-transfer', views.SingleTransferListAPIView.as_view(), name="single-transfer"),
    path('single-transfer/<str:id>', views.SingleTransferListAPIView.as_view(), name="single-transfer-detail"),

    # Bulk Transfer
    path('bulk-transfer-request/create', views.BulkTransferRequestAPIView.as_view(), name="bulk-transfer-request"),
    path('bulk-transfer-request/list', views.BulkTransferRequestListAPIView.as_view(), name="bulk-transfer-request-list"),
    path('bulk-transfer-request/list/<str:id>', views.BulkTransferRequestListAPIView.as_view(), name="bulk-transfer-request-list-detail"),
    path('bulk-transfer', views.BulkTransferListAPIView.as_view(), name="bulk-transfer"),
    path('bulk-transfer/<str:id>', views.BulkTransferListAPIView.as_view(), name="bulk-transfer-detail"),

    # Beneficiaries
    path('transfer-beneficiary/add', views.AddTransferBeneficiaryAPIView.as_view(), name="transfer-beneficiary-add"),
    path('transfer-beneficiary/list', views.TransferBeneficiaryListAPIView.as_view(), name="transfer-beneficiary-list"),
    path('transfer-beneficiary/list/<str:id>', views.TransferBeneficiaryListAPIView.as_view(), name="transfer-beneficiary-list-detail"),

    # Statement Download
    path('download-statement', views.StatementDownloadAPIView.as_view(), name="download-statement")
]

