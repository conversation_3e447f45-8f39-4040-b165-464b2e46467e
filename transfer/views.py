import decimal

from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response

from bowenmfb.modules.bankone import BankOneClient
from bowenmfb.modules.exceptions import InvalidRequestException
from bowenmfb.modules.paginations import CustomPagination
from .serializers import *

client = BankOneClient()


class ListBankGenericAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BankListSerializerOut
    queryset = BankList.objects.all().order_by("name")
    pagination_class = CustomPagination


class NameEnquiryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="bank_id", type=str, required=True), OpenApiParameter(name="account_number", type=str, required=True),
                    OpenApiParameter(name="beneficiary_type", type=str, required=True)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        bank_id = request.GET.get("bank_id")
        account_no = request.GET.get("account_number")
        enquiry_type = request.GET.get("beneficiary_type", "intra")  # intra or inter

        session_id = ""
        account_name = ""
        bank_name = "Bowen MFB"

        if not (len(account_no) == 10 and str(account_no).isnumeric()):
            raise InvalidRequestException({"detail": "Please provide a valid account number"})

        if enquiry_type == "intra":
            response = client.get_customer_by_account_number(account_no)
            print(response)
            if "Name" in response and response["Name"] is not None or "":
                account_name = response["Name"]
            elif "BusinessName" in response and response["BusinessName"] is not None or "":
                account_name = response["BusinessName"]
            elif ("LastName" and "OtherNames" in response) and (response["LastName"] is not None or response["OtherNames"] is not None):
                account_name = str(response["LastName"]) + " " + str(response["OtherNames"])
            else:
                raise InvalidRequestException({"detail": "Account not found"})

        elif enquiry_type == "inter":
            if not bank_id:
                raise InvalidRequestException({"detail": "Please select a bank to continue"})
            try:
                bank = BankList.objects.get(id=bank_id)
            except BankList.DoesNotExist:
                raise InvalidRequestException({"detail": "Invalid bank selection"})
            bank_list_code = bank.code
            bank_name = bank.name
            response = client.name_enquiry(account_number=account_no, bank_code=bank_list_code)
            if "IsSuccessful" in response and response["IsSuccessful"] is True:
                account_name = response["Name"]
                session_id = response["SessionID"]
            else:
                raise InvalidRequestException({"detail": "Account not found"})
        else:
            raise InvalidRequestException({"detail": "Invalid beneficiary type. Choices: 'intra' or 'inter'"})

        return Response({
            "detail": "Account information retrieved", "data": {"bank_name": bank_name, "account_name": account_name, "session_id": session_id}
        })


class AccountBalanceAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company = request.user.customer.company
        if not company:
            raise InvalidRequestException({"detail": "You need to be part of a company to view balances"})

        result = list()
        for account in CompanyAccount.objects.filter(company=company):
            account_number = account.account_number
            available = 0.0
            ledger = 0.0
            withdraw_able = 0.0
            response = client.get_account_by_number(account_number)
            if "AvailableBalance" in response:
                available = response["AvailableBalance"]
            if "LedgerBalance" in response:
                ledger = response["LedgerBalance"]
            if "WithdrawableBalance" in response:
                withdraw_able = response["WithdrawableBalance"]

            balance_data = {
                "account_number": account_number,
                "balances": {
                    "available_balance": available,
                    "ledger_balance": ledger,
                    "withdrawable_balance": withdraw_able
                }
            }
            result.append(balance_data)
        return Response({"detail": "Account balances retrieved", "data": result})


class TransactionHistoryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="date_from", type=str), OpenApiParameter(name="account_number", type=str, required=True),
                    OpenApiParameter(name="date_to", type=str), OpenApiParameter(name="page_no", type=str)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        account_no = request.GET.get("account_number")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        page_no = request.GET.get("page_no")

        company = request.user.customer.company

        if not CompanyAccount.objects.filter(account_number=account_no, company=company).exists():
            raise InvalidRequestException({"detail": "Account number not valid"})

        response = client.paginated_transaction_history(
            account_number=account_no, page_number=page_no, transaction_date_from=date_from, transaction_date_to=date_to
        )
        result = list()
        pages = dict()

        if "IsSuccessful" in response and response["IsSuccessful"] is True:
            messages = response["Message"]["data"]
            paging = response["Message"]["page"]
            pages["size"] = paging["size"]
            pages["item_count"] = paging["totalCount"]
            pages["page_count"] = paging["totalPages"]
            for item in messages:
                message = dict()
                message["date"] = item["TransactionDate"]
                message["date_string"] = item["TransactionDateString"]
                message["direction"] = item["RecordType"]
                message["amount"] = decimal.Decimal(item["Amount"]) / 100
                message["description"] = item["Narration"]
                message["reference_no"] = item["InstrumentNo"]
                result.append(message)

        return Response({"detail": "Transaction history retrieved successfully", "result": result, "pagination": pages})



