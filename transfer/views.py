import decimal
import os

from django.http import Http404, HttpResponse
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response

from bowenmfb.modules.exceptions import raise_serializer_error_msg
from bowenmfb.modules.permissions import TooManyPINRetriesException
from bowenmfb.modules.paginations import CustomPagination
from .serializers import *

client = BankOneClient()


class ListBankGenericAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BankListSerializerOut
    queryset = BankList.objects.all().order_by("name")
    # pagination_class = CustomPagination


class NameEnquiryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="bank_id", type=str, required=False), OpenApiParameter(name="account_number", type=str, required=True),
                    OpenApiParameter(name="beneficiary_type", type=str, required=True)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        bank_id = request.GET.get("bank_id")
        account_no = request.GET.get("account_number")
        enquiry_type = request.GET.get("beneficiary_type", "intra")  # intra or inter

        session_id = ""
        account_name = ""
        bank_name = "Bowen MFB"

        if not (len(account_no) == 10 and str(account_no).isnumeric()):
            raise InvalidRequestException({"detail": "Please provide a valid account number"})

        if enquiry_type == "intra":
            response = client.get_customer_by_account_number(account_no)
            print(response)
            if "Name" in response and response["Name"] is not None or "":
                account_name = response["Name"]
            elif "BusinessName" in response and response["BusinessName"] is not None or "":
                account_name = response["BusinessName"]
            elif ("LastName" and "OtherNames" in response) and (response["LastName"] is not None or response["OtherNames"] is not None):
                account_name = str(response["LastName"]) + " " + str(response["OtherNames"])
            else:
                raise InvalidRequestException({"detail": "Account not found"})

        elif enquiry_type == "inter":
            if not bank_id:
                raise InvalidRequestException({"detail": "Please select a bank to continue"})
            try:
                bank = BankList.objects.get(id=bank_id)
            except BankList.DoesNotExist:
                raise InvalidRequestException({"detail": "Invalid bank selection"})
            bank_list_code = bank.code
            bank_name = bank.name
            response = client.name_enquiry(account_number=account_no, bank_code=bank_list_code)
            if "IsSuccessful" in response and response["IsSuccessful"] is True:
                account_name = response["Name"]
                session_id = response["SessionID"]
                if settings.DEBUG is True:
                    session_id = "23456789876543234567898765432345"
            else:
                raise InvalidRequestException({"detail": "Account not found"})
        else:
            raise InvalidRequestException({"detail": "Invalid beneficiary type. Choices: 'intra' or 'inter'"})

        return Response({
            "detail": "Account information retrieved", "data": {"bank_name": bank_name, "account_name": account_name, "session_id": session_id}
        })


class AccountBalanceAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        company = request.user.customer.company
        if not company:
            raise InvalidRequestException({"detail": "You need to be part of a company to view balances"})

        result = list()
        for account in CompanyAccount.objects.filter(company=company):
            account_number = account.account_number
            balance_data = get_account_balance(account_number)
            result.append(balance_data)
        return Response({"detail": "Account balances retrieved", "data": result})


class TransactionHistoryAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="date_from", type=str), OpenApiParameter(name="account_number", type=str, required=True),
                    OpenApiParameter(name="date_to", type=str), OpenApiParameter(name="page_no", type=str)],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        account_no = request.GET.get("account_number")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        page_no = request.GET.get("page_no")

        company = request.user.customer.company

        if not CompanyAccount.objects.filter(account_number=account_no, company=company).exists():
            raise InvalidRequestException({"detail": "Account number not valid"})

        response = client.paginated_transaction_history(
            account_number=account_no, page_number=page_no, transaction_date_from=date_from, transaction_date_to=date_to
        )
        result = list()
        pages = dict()

        if "IsSuccessful" in response and response["IsSuccessful"] is True:
            messages = response["Message"]["data"]
            paging = response["Message"]["page"]
            pages["size"] = paging["size"]
            pages["item_count"] = paging["totalCount"]
            pages["page_count"] = paging["totalPages"]
            for item in messages:
                message = dict()
                message["date"] = item["TransactionDate"]
                message["date_string"] = item["TransactionDateString"]
                message["direction"] = item["RecordType"]
                message["amount"] = decimal.Decimal(item["Amount"]) / 100
                message["description"] = item["Narration"]
                message["reference_no"] = item["InstrumentNo"]
                result.append(message)

        return Response({"detail": "Transaction history retrieved successfully", "result": result, "pagination": pages})


class AccountSignatoryListAPIView(generics.ListCreateAPIView):
    """
    List and create account signatories for company accounts.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AccountSignatorySerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        user = self.request.user
        company = user.customer.company
        account_id = self.request.query_params.get('account_id')

        queryset = AccountSignatory.objects.filter(
            company_account__company=company,
            is_active=True
        )

        if account_id:
            queryset = queryset.filter(company_account_id=account_id)

        return queryset.order_by('hierarchy_level')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AccountSignatorySerializerIn
        return AccountSignatorySerializerOut

    @extend_schema(responses={status.HTTP_200_OK: AccountSignatorySerializerOut(many=True)})
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(request=AccountSignatorySerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request, *args, **kwargs):
        # Ensure user can only create signatories for their company's accounts
        company = request.user.customer.company
        account_id = request.data.get('company_account')

        if not CompanyAccount.objects.filter(id=account_id, company=company).exists():
            raise InvalidRequestException({"detail": "Invalid account selection"})

        return super().post(request, *args, **kwargs)


class SignatoryHierarchyAPIView(APIView):
    """
    Manage signatory hierarchy for company accounts.
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="account_id", type=str, required=True)],
        responses={status.HTTP_200_OK: SignatoryHierarchySerializerOut}
    )
    def get(self, request):
        account_id = request.GET.get("account_id")
        company = request.user.customer.company

        try:
            account = CompanyAccount.objects.get(id=account_id, company=company)
            hierarchy, created = SignatoryHierarchy.objects.get_or_create(
                company_account=account,
                defaults={'total_levels': 1, 'is_single_signatory': True}
            )
            serializer = SignatoryHierarchySerializerOut(hierarchy)
            return Response({
                "detail": "Signatory hierarchy retrieved successfully",
                "data": serializer.data
            })
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})

    @extend_schema(request=SignatoryHierarchySerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        account_id = request.data.get("account_id")
        company = request.user.customer.company

        try:
            account = CompanyAccount.objects.get(id=account_id, company=company)
            hierarchy, created = SignatoryHierarchy.objects.get_or_create(
                company_account=account
            )

            serializer = SignatoryHierarchySerializerIn(hierarchy, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    "detail": "Signatory hierarchy updated successfully",
                    "data": SignatoryHierarchySerializerOut(hierarchy).data
                })
            else:
                raise InvalidRequestException({"detail": serializer.errors})
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})


class TransferApprovalWorkflowAPIView(APIView):
    """
    View and manage transfer approval workflows.
    """
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="transfer_type", type=str, required=True, description="'single' or 'bulk'"),
            OpenApiParameter(name="transfer_id", type=str, required=True, description="uuid of the single or bulk transfer request")

        ],
        responses={status.HTTP_200_OK: TransferApprovalWorkflowSerializerOut}
    )
    def get(self, request):
        transfer_type = request.GET.get("transfer_type")
        transfer_id = request.GET.get("transfer_id")
        company = request.user.customer.company

        try:
            if transfer_type == "single":
                transfer = SingleTransferRequest.objects.get(id=transfer_id, company=company)
                workflow = getattr(transfer, 'approval_workflow', None)
            elif transfer_type == "bulk":
                transfer = BulkTransferRequest.objects.get(id=transfer_id, company=company)
                workflow = getattr(transfer, 'approval_workflow', None)
            else:
                raise InvalidRequestException({"detail": "Invalid transfer type. Use 'single' or 'bulk'"})

            if not workflow:
                raise InvalidRequestException({"detail": "Workflow not found for this transfer"})

            serializer = TransferApprovalWorkflowSerializerOut(workflow)
            return Response({
                "detail": "Transfer approval retrieved successfully",
                "data": serializer.data
            })
        except (SingleTransferRequest.DoesNotExist, BulkTransferRequest.DoesNotExist):
            raise InvalidRequestException({"detail": "Transfer not found"})


class ProcessTransferApprovalAPIView(APIView):
    """
    Process transfer approval actions (check, verify, approve, decline).
    """
    permission_classes = [TooManyPINRetriesException]

    @extend_schema(request=ProcessTransferApprovalSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ProcessTransferApprovalSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class BulkTransferTemplateDownloadAPIView(APIView):
    """
    Download the CSV template for bulk transfers.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        template_path = os.path.join(settings.BASE_DIR, 'static', 'templates', 'bulk_transfer_template.csv')

        if not os.path.exists(template_path):
            raise Http404("Template file not found")

        with open(template_path, 'rb') as template_file:
            response = HttpResponse(template_file.read(), content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="bulk_transfer_template.csv"'
            return response


class CreateSingleTransferRequestAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=CreateSingleTransferSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CreateSingleTransferSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"detail": "Transfer request submitted successfully", "data": response})


class ListSingleTransferRequest(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SingleTransferRequestSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        return SingleTransferRequest.objects.filter(company_id=self.request.user.customer.company_id)


class SingleTransferListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SingleTransferSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        return SingleTransfer.objects.filter(company_id=self.request.user.customer.company_id).order_by("-created_on")


class BulkTransferListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = BulkTransferSerializerOut
    pagination_class = CustomPagination
    lookup_field = "id"

    def get_queryset(self):
        return BulkTransfer.objects.filter(company_id=self.request.user.customer.company_id).order_by("-created_on")


