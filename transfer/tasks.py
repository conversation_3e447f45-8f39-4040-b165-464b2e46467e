from celery import shared_task
from django.conf import settings

from bowenmfb.modules.utils import generate_transaction_reference
from transfer.models import TransferApprovalWorkflow, SingleTransfer, BulkTransfer
from bowenmfb.modules.bankone import BankOneClient

client = BankOneClient()


@shared_task
def perform_bowen_fund_transfer(workflow_id):
    try:
        workflow = TransferApprovalWorkflow.objects.get(id=workflow_id, status="approved")
    except TransferApprovalWorkflow.DoesNotExist:
        return False

    if workflow.single_transfer_request:
        transfer_request = workflow.single_transfer_request
    else:
        transfer_request = workflow.bulk_transfer_request

    if SingleTransfer.objects.filter(transfer_request_id=transfer_request.id).exists() or \
            BulkTransfer.objects.filter(bulk_request_id=transfer_request.id).exists():
        workflow.process_response = "Transfer already exist/completed"
        workflow.save()
        return False

    if workflow.status != "approved":
        workflow.process_response = "Workflow has already been approved"
        workflow.save()
        return False

    # Create transaction request
    ref_number = generate_transaction_reference()
    if workflow.single_transfer_request:
        single_request = workflow.single_transfer_request
        company = single_request.company
        beneficiary_type = single_request.beneficiary_type
        sending_account = single_request.from_account
        amount = single_request.amount
        receiver_account_number = single_request.beneficiary_account_number
        receiver_name = single_request.beneficiary_name

        company_name_short = str(company.name)[:10]
        narration_short = str(single_request.description)[:10]

        description = f"BOWENMFB/{company_name_short}/{narration_short}"
        transfer = SingleTransfer.objects.create(
            company=company, transfer_request=single_request, from_account_id=sending_account.id,
            transfer_type=beneficiary_type, beneficiary_name=receiver_name, beneficiary_bank_name=single_request.bank_name,
            beneficiary_acct_number=receiver_account_number, amount=amount, narration=single_request.description,
            reference=ref_number
        )
        # Call Core Banking to Send Fund
        sender_account_number = sending_account.account_number

        if beneficiary_type == "intra":
            intra_response = client.local_funds_transfer(
                from_account_number=sender_account_number, amount=amount, to_account_number=receiver_account_number,
                retrieval_reference=ref_number, narration=description
            )
            if ("IsSuccessful" and "ResponseCode" in intra_response) and \
                    (intra_response["IsSuccessful"] is True and intra_response["ResponseCode"] == "00"):
                # Transfer successful
                transfer.status = "success"
                transfer.provider_reference = intra_response["Reference"]
            else:
                transfer.status = "failed"
            transfer.save()
            single_request.provider_response = intra_response
            single_request.save()

        elif beneficiary_type == "inter":
            bank_code = single_request.beneficiary_bank_code
            bank_account_identifier = sending_account.bank_one_account_number
            nip_session = single_request.nip_session_id
            if settings.DEBUG is True:
                inter_response = {
                                    "Status": "Successful",
                                    "StatusDescription": None,
                                    "ReferenceID": 0,
                                    "UniqueIdentifier": "020067152410012328260000000000000000000000",
                                    "IsSuccessFul": True,
                                    "ResponseMessage": None,
                                    "ResponseCode": "00",
                                    "RequestStatus": True,
                                    "ResponseDescription": None,
                                    "ResponseStatus": "Successful"
                                }
            else:
                inter_response = client.inter_bank_transfer(
                    amount=amount, payer=str(company.name).upper(), payer_account_number=sender_account_number,
                    receiver_account_number=receiver_account_number, receiver_bank_code=bank_code, narration=description,
                    transaction_reference=ref_number, receiver_name=receiver_name, nip_session_id=nip_session, appzone_account=bank_account_identifier
                )

            if ("Status" in inter_response and inter_response["Status"] in ["SuccessfulButFeeNotTaken", "SuccesfulButFeeNotTaken", "Successful"])\
                    and ("ResponseCode" in inter_response and inter_response["ResponseCode"] == "00"):
                # Transfer Successful
                transfer.status = "success"
                transfer.provider_reference = inter_response["UniqueIdentifier"]
            elif ("Status" in inter_response and inter_response["Status"] == "Pending") or \
                    ("ResponseCode" in inter_response and inter_response["ResponseCode"] in ["91", "06"]):
                # Transaction Status Query is required
                ...
            else:
                transfer.status = "failed"
            transfer.save()
            single_request.provider_response = inter_response
            single_request.save()

        else:
            workflow.process_response = "Invalid transfer or beneficiary type"
            workflow.save()
            return False

    if workflow.bulk_transfer_request:
        ...

    return True
