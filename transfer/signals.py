from django.db.models.signals import post_save
from django.dispatch import receiver

from .models import SignatoryHierarchy, CompanyAccount, Customer, AccountSignatory


@receiver(signal=post_save, sender=CompanyAccount)
def create_account_hierarchy(sender, instance, **kwargs):
    company = instance.company
    hierarchy, _ = SignatoryHierarchy.objects.get_or_create(company_account=instance)
    signatories = Customer.objects.filter(company=company)
    for customer in signatories:
        account_signatory, _ = AccountSignatory.objects.get_or_create(company_account=instance, customer=customer)
    total_company_signatories = len(signatories)
    hierarchy.total_levels = total_company_signatories
    if total_company_signatories > 1:
        hierarchy.is_single_signatory = False
        hierarchy.requires_approver = True
        hierarchy.requires_verifier = True
        hierarchy.requires_checker = True
    hierarchy.save()

    return True


