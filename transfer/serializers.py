from django.conf import settings
from django.utils import timezone
from rest_framework import serializers

from bowenmfb.modules.exceptions import InvalidRequestException
from bowenmfb.modules.utils import get_account_balance
from transfer.models import *
from transfer.tasks import *


class BankListSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BankList
        exclude = []


class TransferBeneficiarySerializerOut(serializers.ModelSerializer):
    class Meta:
        model = TransferBeneficiary
        exclude = []


class TransferSchedulerSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = TransferScheduler
        exclude = []


class SingleTransferRequestSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = SingleTransferRequest
        depth = 1
        exclude = []


class SingleTransferSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = SingleTransfer
        exclude = []


class BulkTransferRequestSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BulkTransferRequest
        exclude = []


class BulkTransferSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = BulkTransfer
        exclude = []


class AccountSignatorySerializerOut(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.user.get_full_name', read_only=True)
    customer_email = serializers.CharField(source='customer.user.email', read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)

    class Meta:
        model = AccountSignatory
        exclude = []


class AccountSignatorySerializerIn(serializers.ModelSerializer):
    class Meta:
        model = AccountSignatory
        fields = ['customer', 'role', 'hierarchy_level', 'can_upload', 'can_check', 'can_verify', 'can_approve']


class SignatoryHierarchySerializerOut(serializers.ModelSerializer):
    signatories = AccountSignatorySerializerOut(many=True, read_only=True)

    class Meta:
        model = SignatoryHierarchy
        exclude = []


class SignatoryHierarchySerializerIn(serializers.ModelSerializer):
    class Meta:
        model = SignatoryHierarchy
        fields = ['total_levels', 'requires_checker', 'requires_verifier', 'requires_approver', 'is_single_signatory']


class TransferApprovalWorkflowSerializerOut(serializers.ModelSerializer):
    uploaded_by_name = serializers.CharField(source='uploaded_by.user.get_full_name', read_only=True)
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    declined_by_name = serializers.CharField(source='declined_by.user.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = TransferApprovalWorkflow
        exclude = []


class ProcessTransferApprovalSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    action = serializers.ChoiceField(choices=['check', 'verify', 'approve', 'decline'])
    remarks = serializers.CharField(required=False, allow_blank=True)
    approval_id = serializers.UUIDField()
    transaction_pin = serializers.CharField()

    def validate(self, attrs):
        user = attrs.get('user')
        transaction_pin = attrs.get('transaction_pin')
        action = attrs.get('action')
        remarks = attrs.get('remarks', '')

        customer = user.customer

        decrypted_pin = str(customer.get_decrypted_approval_pin)
        if decrypted_pin != transaction_pin:
            customer.failed_pin_retries += 1
            customer.save()
            raise InvalidRequestException({"detail": "Incorrect Transaction PIN"})

        if action == 'decline' and not remarks:
            raise InvalidRequestException({"detail": "Remarks are required when declining a transfer"})

        return attrs

    def create(self, validated_data):
        workflow_id = validated_data.get("approval_id")
        user = validated_data.get("user")
        action = validated_data.get("action")
        remarks = validated_data.get("remarks")

        try:
            workflow = TransferApprovalWorkflow.objects.get(id=workflow_id, status__in=["pending", "in_progress"])
        except TransferApprovalWorkflow.DoesNotExist:
            raise InvalidRequestException({"detail": "Transfer approval not found"})

        # Check if user can perform this action
        if not workflow.can_user_perform_action(user, action):
            raise InvalidRequestException({
                "detail": f"You are not authorized to {action} this transfer at the current level"
            })

        # Process the action
        current_time = timezone.now()
        customer = user.customer
        customer.failed_pin_retries = 0
        customer.save()

        if action == "check":
            workflow.checked_by = customer
            workflow.checked_at = current_time
            workflow.current_level = 3
            workflow.status = "in_progress"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_checked = True
                workflow.single_transfer_request.checked_by = customer
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_checked = True
                workflow.bulk_transfer_request.checked_by = customer
                workflow.bulk_transfer_request.save()

        elif action == "verify":
            workflow.verified_by = customer
            workflow.verified_at = current_time
            workflow.current_level = 4
            workflow.status = "in_progress"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_verified = True
                workflow.single_transfer_request.verified_by = customer
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_verified = True
                workflow.bulk_transfer_request.verified_by = customer
                workflow.bulk_transfer_request.save()

        elif action == "approve":
            workflow.approved_by = customer
            workflow.approved_at = current_time
            workflow.status = "approved"
            workflow.save()

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_approved = True
                workflow.single_transfer_request.approved_by = customer
                # Send workflow to celery
                perform_bowen_fund_transfer.delay(str(workflow.id))
                # perform_bowen_fund_transfer(str(workflow.id))
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_approved = True
                workflow.bulk_transfer_request.approved_by = customer
                workflow.bulk_transfer_request.save()

        elif action == "decline":
            workflow.declined_by = customer
            workflow.declined_at = current_time
            workflow.decline_reason = remarks
            workflow.status = "declined"

            # Update the transfer request
            if workflow.single_transfer_request:
                workflow.single_transfer_request.is_declined = True
                workflow.single_transfer_request.declined_by = customer
                workflow.single_transfer_request.decline_reason = remarks
                workflow.single_transfer_request.save()
            elif workflow.bulk_transfer_request:
                workflow.bulk_transfer_request.is_declined = True
                workflow.bulk_transfer_request.declined_by = customer
                workflow.bulk_transfer_request.decline_reason = remarks
                workflow.bulk_transfer_request.save()

        workflow.save()

        return {"detail": f"Transfer {action}ed successfully", "data": TransferApprovalWorkflowSerializerOut(workflow).data}


class TransferSchedulerSerializerIn(serializers.Serializer):
    schedule_type = serializers.ChoiceField(required=False, choices=SCHEDULE_TYPE, default="once")
    day_of_the_month = serializers.ChoiceField(required=False, choices=DAYS_OF_THE_MONTH_CHOICES, default="1")
    day_of_the_week = serializers.ChoiceField(required=False, choices=DAY_OF_THE_WEEK_CHOICES, default="1")
    status = serializers.ChoiceField(required=False, choices=TRANSFER_SCHEDULE_STATUS, default="inactive")


class CreateSingleTransferSerializerIn(TransferSchedulerSerializerIn):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    beneficiary_type = serializers.ChoiceField(choices=TRANSFER_BENEFICIARY_TYPE_CHOICES)
    amount = serializers.FloatField(min_value=5)
    from_account_id = serializers.UUIDField()
    to_account_number = serializers.CharField()
    to_account_name = serializers.CharField()
    to_bank_name = serializers.CharField()
    to_bank_code = serializers.CharField(required=False, allow_null=False, allow_blank=False)
    narration = serializers.CharField()
    session_id = serializers.CharField(required=False, allow_null=False, allow_blank=False)
    schedule = serializers.BooleanField(default=False)

    def validate(self, attrs):
        user = attrs.get("user")
        from_account = attrs.get("from_account_id")
        amount = attrs.get("amount")
        session_id = attrs.get("session_id")
        bank_code = attrs.get("to_bank_code")
        transfer_type = attrs.get("transfer_type")
        # Validate user is member of from_account
        try:
            from_acct = CompanyAccount.objects.get(id=from_account, company=user.customer.company)
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Selected account not valid"})

        # Validate from_account balance is sufficient to perform transfer
        account_number = from_acct.account_number
        response = get_account_balance(account_number)
        balance = response.get("balances").get("withdrawable_balance")
        if settings.DEBUG is True:
            balance = response.get("balances").get("available_balance")

        if amount > float(balance):
            raise InvalidRequestException({"detail": "Insufficient balance"})

        # Confirm session_id for inter-bank transfer
        if transfer_type == "inter":
            if not session_id:
                raise InvalidRequestException({"detail": "SessionID is required for Inter-Bank Transaction"})
            if not bank_code:
                raise InvalidRequestException({"detail": "Receiving Bank is required for Inter-Bank Transaction"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        transfer_type = validated_data.get("beneficiary_type")
        amount = validated_data.get("amount")
        from_account_id = validated_data.get("from_account_id")
        to_account_number = validated_data.get("to_account_number")
        to_account_name = str(validated_data.get("to_account_name")).upper()
        to_bank_name = str(validated_data.get("to_bank_name")).upper()
        to_bank_code = validated_data.get("to_bank_code")
        narration = str(validated_data.get("narration")).upper()
        session_id = validated_data.get("session_id", "")
        schedule = validated_data.get("schedule", False)

        bank_name = "BOWEN MFB"
        if transfer_type == "inter":
            bank_name = to_bank_name

        customer = user.customer

        transfer_request = SingleTransferRequest.objects.create(
            company=customer.company, amount=amount, description=narration, from_account_id=from_account_id,
            beneficiary_account_number=to_account_number, beneficiary_name=to_account_name, beneficiary_bank_code=to_bank_code,
            nip_session_id=session_id, bank_name=bank_name, scheduled=schedule, beneficiary_type=transfer_type, created_by=customer
        )
        if schedule:
            schedule_type = validated_data.get("schedule_type")
            dom = validated_data.get("day_of_the_month")
            dow = validated_data.get("day_of_the_week")
            scheduler_status = validated_data.get("status")
            scheduler = TransferScheduler.objects.create(
                schedule_type=schedule_type, day_of_the_month=dom, day_of_the_week=dow, status=scheduler_status
            )
            transfer_request.scheduler = scheduler
            transfer_request.save()

        # Create TransferApprovalWorkflow
        TransferApprovalWorkflow.objects.create(single_transfer_request=transfer_request, uploaded_by=customer, uploaded_at=transfer_request.created_at)

        return SingleTransferRequestSerializerOut(transfer_request).data




